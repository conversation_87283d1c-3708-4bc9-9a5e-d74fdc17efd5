apiVersion: kubernetes.dask.org/v1
kind: DaskCluster
spec:
  worker:
    replicas: 0
    spec:
      containers:
        - name: worker
          imagePullPolicy: "IfNotPresent"
          command: ["/bin/sh", "-c"]
          ports:
            - name: http-dashboard
              containerPort: 8788
              protocol: TCP
  scheduler:
    spec:
      containers:
        - name: scheduler
          imagePullPolicy: "IfNotPresent"
          args:
            - dask-scheduler
          ports:
            - name: tcp-comm
              containerPort: 8786
              protocol: TCP
            - name: http-dashboard
              containerPort: 8787
              protocol: TCP
          readinessProbe:
            httpGet:
              port: http-dashboard
              path: /health
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            httpGet:
              port: http-dashboard
              path: /health
            initialDelaySeconds: 15
            periodSeconds: 20
    service:
      type: ClusterIP
      selector:
        dask.org/component: scheduler
      ports:
        - name: tcp-comm
          protocol: TCP
          port: 8786
          targetPort: "tcp-comm"
        - name: http-dashboard
          protocol: TCP
          port: 8787
          targetPort: "http-dashboard"
