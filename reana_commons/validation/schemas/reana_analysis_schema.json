{"$schema": "http://json-schema.org/draft-07/schema", "$defs": {}, "$id": "reana_spec", "type": "object", "title": "REANA analysis specification", "description": "Full analysis specification including data, sofware, environment and workflow enabling reproducibility on a REANA cluster.", "required": ["workflow"], "additionalProperties": false, "properties": {"version": {"$id": "/properties/version", "type": "string", "title": "REANA version.", "description": "REANA platform version to which the analysis was written for."}, "inputs": {"$id": "/properties/inputs", "type": "object", "title": "Analysis inputs.", "additionalProperties": false, "properties": {"files": {"$id": "/properties/inputs/properties/files", "type": "array", "title": "Analysis input files.", "description": "List of files provided as input for a given analysis.", "items": {"$id": "/properties/inputs/properties/files/items", "type": "string", "title": "Relative path to the file."}}, "directories": {"$id": "/properties/inputs/properties/directories", "type": "array", "title": "Analysis input directories.", "description": "List of directories provided as input for a given analysis.", "items": {"$id": "/properties/inputs/properties/directories/items", "type": "string", "title": "Relative path to the directory."}}, "parameters": {"$id": "/properties/inputs/properties/parameters", "type": "object", "title": "Analysis parameters.", "description": "Key value data structure which represents the analysis parameters."}, "options": {"$id": "/properties/workflow/properties/options", "type": "object", "title": "Workflow operational options.", "description": "Extra operational options accepted by workflow engines."}}}, "workflow": {"$id": "/properties/workflow", "type": "object", "title": "Analysis workflow.", "description": "Workflow which represents the steps that need to be run to reproduce an analysis.", "additionalProperties": false, "properties": {"specification": {"$id": "/properties/workflow/properties/specification", "type": "object", "title": "Workflow specification in yaml format."}, "file": {"$id": "/properties/workflow/properties/file", "type": "string", "title": "Workflow file name."}, "type": {"$id": "/properties/workflow/properties/type", "type": "string", "title": "Workflow engine.", "description": "Name which represents a supported workflow engine to be used to reproduce the analysis.", "enum": ["cwl", "serial", "yadage", "snakemake"]}, "resources": {"$id": "/properties/workflow/properties/resources", "type": "object", "title": "Workflow resources in yaml format.", "properties": {"cvmfs": {"$id": "/properties/workflow/properties/resources/properties/cvmfs", "type": "array", "items": {"type": "string", "title": "CVMFS repos", "minLength": 1, "maxLength": 60, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9.\\-_]*$"}, "uniqueItems": true}, "kerberos": {"$id": "/properties/workflow/properties/resources/properties/kerberos", "type": "boolean", "title": "Kerberos authentication for the whole workflow."}, "voms_proxy": {"$id": "/properties/workflow/properties/resources/properties/voms_proxy", "type": "boolean", "title": "VOMS proxy authentication for the whole workflow."}, "rucio": {"id": "/properties/workflow/properties/resources/properties/rucio", "type": "boolean", "title": "Rucio integration"}, "dask": {"$id": "/properties/workflow/properties/resources/properties/dask", "type": "object", "title": "Information about Dask cluster requested for the analysis.", "properties": {"image": {"type": "string", "description": "Container image to be used by Dask scheduler and workers.", "pattern": "^(?:[a-zA-Z0-9.-]+(?:\\.[a-zA-Z0-9.-]+)+(?::[0-9]+)?/)?[a-zA-Z0-9._-]+(?:/[a-zA-Z0-9._-]+)*(?::[a-zA-Z0-9._-]+)?(?:@sha256:[a-f0-9]{64})?$"}, "number_of_workers": {"type": "integer", "description": "Requested number of Dask workers."}, "single_worker_memory": {"type": "string", "description": "Requested memory for one Dask worker.", "pattern": "^[1-9][0-9]*(Ei|Pi|Ti|Gi|Mi|Ki|E|P|T|G|M|K)$"}, "single_worker_threads": {"type": "integer", "description": "Requested number of threads for one Dask worker."}}, "required": ["image"]}}, "additionalProperties": false}}, "anyOf": [{"required": ["type", "file"]}, {"required": ["type", "specification"]}]}, "outputs": {"$id": "/properties/outputs", "type": "object", "title": "Analysis outputs.", "additionalProperties": false, "properties": {"files": {"$id": "/properties/outputs/properties/files", "type": "array", "title": "Analysis result files.", "description": "Expected output from analysis represented by a set of files.", "items": {"$id": "/properties/outputs/properties/files/items", "type": "string", "title": "Relative path to the file."}}, "directories": {"$id": "/properties/outputs/properties/directories", "type": "array", "title": "Analysis result directories.", "description": "Expected output from analysis represented by a set of directories.", "items": {"$id": "/properties/outputs/properties/directories/items", "type": "string", "title": "Relative path to the directory."}}}}, "tests": {"$id": "/properties/tests", "type": "object", "title": "Analysis tests.", "additionalProperties": false, "properties": {"files": {"$id": "/properties/tests/properties/files", "type": "array", "title": "Analysis test files.", "description": "Gherkin test feature files.", "items": {"$id": "/properties/tests/properties/files/items", "type": "string", "title": "Relative path to the file."}}}}, "workspace": {"$id": "/properties/workspace", "type": "object", "title": "Workflow workspace.", "description": "Full workspace path in which the workflow will run.", "additionalProperties": false, "properties": {"root_path": {"$id": "/properties/workspace/properties/root_path", "type": "string", "title": "Workspace root path."}, "retention_days": {"$id": "/properties/workspace/properties/retention_days", "type": "object", "title": "Retention rules for the files in the workspace.", "minProperties": 1, "additionalProperties": {"type": "integer", "minimum": 0}}}}}, "if": {"properties": {"workflow": {"properties": {"type": {"const": "serial"}}}}}, "then": {"properties": {"workflow": {"properties": {"specification": {"type": "object", "title": "Serial workflow specification.", "description": "Serial workflow specification.", "additionalProperties": false, "properties": {"steps": {"type": "array", "title": "Serial workflow steps.", "description": "List of steps which represent the workflow.", "items": {"type": "object", "title": "Serial workflow step.", "description": "Serial workflow step.", "additionalProperties": false, "properties": {"commands": {"type": "array", "items": {"type": "string"}, "minItems": 1, "title": "Step commands", "description": "List of commands to be run in the step."}, "compute_backend": {"type": "string", "enum": ["kubernetes", "htcondorcern", "slurmcern", "compute4punch"], "title": "Compute backend"}, "environment": {"type": "string", "title": "Container image for the step", "description": "Image to be used by the container in which the step should be run."}, "htcondor_accounting_group": {"type": "string", "title": "HTCondor accounting group"}, "htcondor_max_runtime": {"type": "string", "title": "HTCondor maximum runtime"}, "kerberos": {"type": "boolean", "title": "Use Kerberos authentication", "description": "Whether to use Kerberos authentication for the step. This would require you to upload a valid Kerberos ticket as a REANA secret."}, "kubernetes_job_timeout": {"type": "integer", "title": "Kubernetes job timeout", "description": "Maximum time for the step to run (number of seconds)"}, "kubernetes_memory_limit": {"type": "string", "title": "Kubernetes memory limit", "description": "Kubernetes memory limit (e.g. 256Mi - read more about the expected memory values on the official Kubernetes documentation: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/#meaning-of-memory)"}, "kubernetes_uid": {"type": "integer", "title": "Kubernetes UID", "description": "Kubernetes unique identifier (e.g. 713)"}, "name": {"type": "string", "title": "Step name"}, "rucio": {"type": "boolean", "title": "Rucio integration", "description": "Whether to use Rucio integration."}, "unpacked_img": {"type": "boolean", "title": "Unpacked container image", "description": "Whether to use an unpacked container image. Useful for Singularity images stored on CVMFS"}, "voms_proxy": {"type": "boolean", "title": "VOMS proxy", "description": "Whether to use a VOMS proxy for the step. This would require you to upload a valid VOMS proxy as a REANA secret."}, "c4p_cpu_cores": {"type": "string", "title": "C4P CPU Cores", "description": "Number of CPU cores requested from Compute4PUNCH for running the task."}, "c4p_memory_limit": {"type": "string", "title": "C4P Memory Limit", "description": "Amount of memory requested from Compute4PUNCH for running the task."}, "c4p_additional_requirements": {"type": "string", "title": "C4P Additional Requirements", "description": "Additional HTCondor requirements like RequestGPUs for running the task."}}, "required": ["commands", "environment"]}}}}}}}}, "else": {"properties": {"workflow": {"properties": {"file": {"type": "string"}}}}}}