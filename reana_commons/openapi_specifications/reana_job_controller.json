{"definitions": {"Job": {"properties": {"cmd": {"type": "string"}, "cvmfs_mounts": {"default": "", "type": "string"}, "docker_img": {"type": "string"}, "job_id": {"type": "string"}, "max_restart_count": {"format": "int32", "type": "integer"}, "restart_count": {"format": "int32", "type": "integer"}, "status": {"type": "string"}}, "required": ["cmd", "docker_img", "job_id", "max_restart_count", "restart_count", "status"], "type": "object"}, "JobRequest": {"properties": {"c4p_additional_requirements": {"type": "string"}, "c4p_cpu_cores": {"type": "string"}, "c4p_memory_limit": {"type": "string"}, "cmd": {"default": "", "type": "string"}, "compute_backend": {"type": "string"}, "cvmfs_mounts": {"default": "", "type": "string"}, "docker_img": {"type": "string"}, "env_vars": {"default": {}, "type": "object"}, "htcondor_accounting_group": {"type": "string"}, "htcondor_max_runtime": {"type": "string"}, "job_name": {"type": "string"}, "kerberos": {"type": "boolean"}, "kubernetes_job_timeout": {"format": "int32", "type": "integer"}, "kubernetes_memory_limit": {"type": "string"}, "kubernetes_uid": {"format": "int32", "type": "integer"}, "prettified_cmd": {"default": "", "type": "string"}, "rucio": {"type": "boolean"}, "shared_file_system": {"default": true, "type": "boolean"}, "slurm_partition": {"type": "string"}, "slurm_time": {"type": "string"}, "unpacked_img": {"type": "boolean"}, "voms_proxy": {"type": "boolean"}, "workflow_uuid": {"type": "string"}, "workflow_workspace": {"type": "string"}}, "required": ["docker_img", "job_name", "workflow_uuid", "workflow_workspace"], "type": "object"}}, "info": {"description": "REANA Job Controller API", "title": "reana-job-controller", "version": "0.9.1a2"}, "paths": {"/apispec": {}, "/job_cache": {"get": {"description": "This resource takes a job specification and the workflow json, and checks if the job to be created, already exists in the cache.", "operationId": "check_if_cached", "parameters": [{"description": "Required. Specification of the job.", "in": "query", "name": "job_spec", "required": true, "type": "string"}, {"description": "Required. Specification of the workflow.", "in": "query", "name": "workflow_json", "required": true, "type": "string"}, {"description": "Required. Path to workflow workspace.", "in": "query", "name": "workflow_workspace", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. Returns boolean depicting if job is in cache.", "examples": {"application/json": {"cached": true, "result_path": "/reana/default/0000/xe2123d/archive/asd213"}}}, "400": {"description": "Request failed. The incoming data specification seems malformed."}, "500": {"description": "Request failed. Internal controller error."}}, "summary": "Returns boolean depicting if job is in cache."}}, "/jobs": {"get": {"description": "This resource is not expecting parameters and it will return a list representing all active jobs in JSON format.", "operationId": "get_jobs", "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. The response contains the list of all active jobs.", "examples": {"application/json": {"jobs": {"************************************": {"cmd": "date", "cvmfs_mounts": ["atlas.cern.ch", "atlas-condb.cern.ch"], "docker_img": "docker.io/library/busybox", "job_id": "************************************", "max_restart_count": 3, "restart_count": 0, "status": "finished"}, "************************************": {"cmd": "date", "cvmfs_mounts": ["atlas.cern.ch", "atlas-condb.cern.ch"], "docker_img": "docker.io/library/busybox", "job_id": "************************************", "max_restart_count": 3, "restart_count": 0, "status": "started"}}}}, "schema": {"items": {"$ref": "#/definitions/Job"}, "type": "array"}}}, "summary": "Returns list of all active jobs."}, "post": {"consumes": ["application/json"], "description": "This resource is expecting JSON data with all the necessary information of a new job.", "operationId": "create_job", "parameters": [{"description": "Information needed to instantiate a Job", "in": "body", "name": "job", "required": true, "schema": {"$ref": "#/definitions/JobRequest"}}], "produces": ["application/json"], "responses": {"201": {"description": "Request succeeded. The job has been launched.", "examples": {"application/json": {"job_id": "************************************"}}, "schema": {"properties": {"job_id": {"type": "string"}}, "type": "object"}}, "400": {"description": "Request failed. The incoming data specification seems malformed."}, "500": {"description": "Request failed. Internal controller error. The job could probably not have been allocated."}}, "summary": "Creates a new job."}}, "/jobs/{job_id}": {"get": {"description": "This resource is expecting the job's UUID as a path parameter. Its information will be served in JSON format.", "operationId": "get_job", "parameters": [{"description": "Required. ID of the job.", "in": "path", "name": "job_id", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. The response contains details about the given job ID.", "examples": {"application/json": {"job": {"cmd": "date", "cvmfs_mounts": ["atlas.cern.ch", "atlas-condb.cern.ch"], "docker_img": "docker.io/library/busybox", "job_id": "************************************", "max_restart_count": 3, "restart_count": 0, "status": "started"}}}, "schema": {"$ref": "#/definitions/Job"}}, "404": {"description": "Request failed. The given job ID does not seem to exist.", "examples": {"application/json": {"message": "The job cdcf48b1-c2f3-4693-8230-b066e088444c doesn't exist"}}}}, "summary": "Returns details about a given job."}}, "/jobs/{job_id}/": {"delete": {"consumes": ["application/json"], "description": "This resource expects the `job_id` of the job to be deleted.", "operationId": "delete_job", "parameters": [{"description": "Required. ID of the job to be deleted.", "in": "path", "name": "job_id", "required": true, "type": "string"}, {"description": "Job compute backend.", "in": "query", "name": "compute_backend", "required": false, "type": "string"}], "produces": ["application/json"], "responses": {"204": {"description": "Request accepted. A request to delete the job has been sent to the\n  compute backend."}, "404": {"description": "Request failed. The given job ID does not seem to exist.", "examples": {"application/json": {"message": "The job cdcf48b1-c2f3-4693-8230-b066e088444c doesn't exist"}}}, "502": {"description": "Request failed. Something went wrong while calling the compute backend.", "examples": {"application/json": {"message": "Connection to compute backend failed: [reason]"}}}}, "summary": "Deletes a given job."}}, "/jobs/{job_id}/logs": {"get": {"description": "This resource is expecting the job's UUID as a path parameter. Its information will be served in JSON format.", "operationId": "get_logs", "parameters": [{"description": "Required. ID of the job.", "in": "path", "name": "job_id", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. The response contains the logs for the given job.", "examples": {"application/json": {"log": "<PERSON><PERSON> May 16 13:52:00 CEST 2017\n"}}}, "404": {"description": "Request failed. The given job ID does not seem to exist.", "examples": {"application/json": {"message": "The job cdcf48b1-c2f3-4693-8230-b066e088444c doesn't exist"}}}}, "summary": "Returns the logs for a given job."}}}, "swagger": "2.0"}