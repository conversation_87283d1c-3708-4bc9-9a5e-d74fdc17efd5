# This file is part of REANA.
# Copyright (C) 2017, 2018, 2019, 2020, 2021, 2024 CERN.
#
# REANA is free software; you can redistribute it and/or modify it
# under the terms of the MIT License; see LICENSE file for more details.

include *.json
include *.md
include *.sh
include *.txt
include *.yaml
include .dockerignore
include .flake8
include Dockerfile
include LICENSE
include pytest.ini
include docs/openapi.json
exclude .editorconfig
exclude .prettierrc
exclude .prettierignore
exclude .readthedocs.yaml
prune docs/_build
recursive-include docs *.py
recursive-include docs *.png
recursive-include docs *.md
recursive-include docs *.txt
recursive-include reana_workflow_controller *.html
recursive-include reana_workflow_controller *.py
recursive-include reana_workflow_controller/templates *.template
recursive-include reana_workflow_controller/templates *.yaml
recursive-include scripts *.py
recursive-include tests *.py
recursive-include tests *.finished
recursive-include tests *.running
recursive-include tests *.waiting
