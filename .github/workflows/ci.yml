# This file is part of REANA.
# Copyright (C) 2020, 2022, 2024 CERN.
#
# REANA is free software; you can redistribute it and/or modify it
# under the terms of the MIT License; see LICENSE file for more details.

name: CI

on: [push, pull_request]

jobs:
  lint-commitlint:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: actions/setup-node@v4

      - name: Install commitlint
        run: |
          npm install conventional-changelog-conventionalcommits
          npm install commitlint@latest

      - name: Check commit message compliance of the recently pushed commit
        if: github.event_name == 'push'
        run: |
          ./run-tests.sh --check-commitlint HEAD~1 HEAD

      - name: Check commit message compliance of the pull request
        if: github.event_name == 'pull_request'
        run: |
          ./run-tests.sh --check-commitlint ${{ github.event.pull_request.base.sha }} ${{ github.event.pull_request.head.sha }} ${{ github.event.pull_request.number }}

  lint-shellcheck:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Runs shell script static analysis
        run: |
          sudo apt-get install shellcheck
          ./run-tests.sh --check-shellcheck

  lint-black:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Check Python code formatting
        run: |
          pip install --upgrade pip
          pip install black
          ./run-tests.sh --check-black

  lint-flake8:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Check compliance with pep8, pyflakes and circular complexity
        run: |
          pip install --upgrade pip
          pip install flake8
          ./run-tests.sh --check-flake8

  lint-pydocstyle:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Check compliance with Python docstring conventions
        run: |
          pip install --upgrade pip
          pip install pydocstyle
          ./run-tests.sh --check-pydocstyle

  lint-check-manifest:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Check Python manifest completeness
        run: |
          pip install --upgrade pip
          pip install check-manifest
          ./run-tests.sh --check-manifest

  format-shfmt:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Check shell script code fomatting
        run: |
          sudo apt-get install shfmt
          ./run-tests.sh --check-shfmt

  lint-yamllint:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Lint YAML files
        run: |
          pip install yamllint
          ./run-tests.sh --check-yamllint

  lint-markdownlint:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4

      - name: Lint Markdown files
        run: |
          npm install markdownlint-cli2 --global
          ./run-tests.sh --check-markdownlint

  format-prettier:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4

      - name: Check Prettier code fomatting
        run: |
          npm install prettier --global
          ./run-tests.sh --check-prettier

  lint-jsonlint:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4

      - name: Lint JSON files
        run: |
          npm install jsonlint --global
          ./run-tests.sh --check-jsonlint

  docs-sphinx:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install system dependencies
        run: |
          sudo apt-get update -y
          sudo apt install libcurl4-openssl-dev libssl-dev
          sudo apt-get install libgnutls28-dev

      - name: Install Python dependencies
        run: |
          pip install --upgrade pip setuptools py
          pip install -e .[all]

      - name: Run Sphinx documentation with doctests
        run: ./run-tests.sh --check-sphinx

  python-tests:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install Python dependencies
        run: |
          pip install --upgrade pip setuptools py
          pip install twine wheel
          pip install -e .[all]

      - name: Generate and check OpenAPI specification
        run: ./run-tests.sh --check-openapi-spec

      - name: Run pytest
        run: |
          mkdir /tmp/reana
          SHARED_VOLUME_PATH=/tmp/reana ./run-tests.sh --check-pytest

      - name: Codecov Coverage
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          files: coverage.xml

  lint-dockerfile:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Check Dockerfile compliance
        run: ./run-tests.sh --check-dockerfile

  docker-build:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Build Docker image
        run: ./run-tests.sh --check-docker-build

  release-docker:
    runs-on: ubuntu-24.04
    if: >
      vars.RELEASE_DOCKER == 'true' && github.event_name == 'push' &&
      startsWith(github.ref, 'refs/tags/')
    needs:
      - docs-sphinx
      - lint-black
      - lint-check-manifest
      - lint-commitlint
      - lint-dockerfile
      - lint-flake8
      - lint-pydocstyle
      - lint-shellcheck
      - python-tests
    steps:
      - name: Release Docker image
        uses: reanahub/reana-github-actions/release-docker@v1
        with:
          username: ${{ secrets.DOCKER_USER }}
          token: ${{ secrets.DOCKER_TOKEN }}
          organisation: ${{ vars.DOCKER_ORGANISATION }}
          registry: ${{ vars.DOCKER_REGISTRY }}
          platform: |
            linux/amd64
            linux/arm64
