# This file is part of REANA.
# Copyright (C) 2020, 2021, 2022, 2023, 2024 CERN.
#
# REANA is free software; you can redistribute it and/or modify it
# under the terms of the MIT License; see LICENSE file for more details.

name: CI

on: [push, pull_request]

jobs:
  lint-commitlint:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: actions/setup-node@v4

      - name: Install commitlint
        run: |
          npm install conventional-changelog-conventionalcommits
          npm install commitlint@latest

      - name: Check commit message compliance of the recently pushed commit
        if: github.event_name == 'push'
        run: |
          ./run-tests.sh --check-commitlint HEAD~1 HEAD

      - name: Check commit message compliance of the pull request
        if: github.event_name == 'pull_request'
        run: |
          ./run-tests.sh --check-commitlint ${{ github.event.pull_request.base.sha }} ${{ github.event.pull_request.head.sha }} ${{ github.event.pull_request.number }}

  lint-shellcheck:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Runs shell script static analysis
        run: |
          sudo apt-get install shellcheck
          ./run-tests.sh --check-shellcheck

  lint-black:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Check Python code formatting
        run: |
          pip install black
          ./run-tests.sh --check-black

  lint-flake8:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Check compliance with pep8, pyflakes and circular complexity
        run: |
          pip install flake8
          ./run-tests.sh --check-flake8

  lint-pydocstyle:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Check compliance with Python docstring conventions
        run: |
          pip install pydocstyle
          ./run-tests.sh --check-pydocstyle

  lint-check-manifest:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Check Python manifest completeness
        run: |
          pip install check-manifest
          ./run-tests.sh --check-manifest

  docs-sphinx:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install system dependencies
        run: |
          sudo apt-get update -y
          sudo apt-get install python3-dev graphviz libgraphviz-dev pkg-config uuid-dev

      - name: Install system dependencies
        run: |
          sudo apt-get update -y
          sudo apt install libcurl4-openssl-dev libssl-dev
          sudo apt-get install libgnutls28-dev

      - name: Install Python dependencies
        run: |
          pip install -e .[all]

      - name: Run Sphinx documentation with doctests
        run: ./run-tests.sh --check-sphinx

  python-tests:
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12", "3.13"]

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install system dependencies
        run: |
          sudo apt-get update -y
          sudo apt-get install python3-dev graphviz libgraphviz-dev pkg-config uuid-dev

      - name: Install Python dependencies
        run: |
          pip install twine wheel
          pip install -e .[all]

      - name: Run pytest
        run: ./run-tests.sh --check-pytest

      - name: Codecov Coverage
        if: matrix.python-version == '3.12'
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          files: coverage.xml
