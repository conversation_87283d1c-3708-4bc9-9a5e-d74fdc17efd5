{"info": {"description": "Submit and manage workflows", "title": "REANA Workflow Controller", "version": "0.95.0a4"}, "paths": {"/api/workflows": {"get": {"description": "This resource is expecting a user UUID. The information related to all workflows for a given user will be served as JSON", "operationId": "get_workflows", "parameters": [{"description": "Required. UUID of workflow owner.", "in": "query", "name": "user", "required": true, "type": "string"}, {"description": "Required. Type of workflows.", "in": "query", "name": "type", "required": true, "type": "string"}, {"description": "Optional flag to show more information.", "in": "query", "name": "verbose", "required": false, "type": "boolean"}, {"description": "Filter workflows by name.", "in": "query", "name": "search", "required": false, "type": "string"}, {"description": "Sort workflows by creation date (asc, desc).", "in": "query", "name": "sort", "required": false, "type": "string"}, {"description": "Filter workflows by list of statuses.", "in": "query", "items": {"type": "string"}, "name": "status", "required": false, "type": "array"}, {"description": "Results page number (pagination).", "in": "query", "name": "page", "required": false, "type": "integer"}, {"description": "Number of results per page (pagination).", "in": "query", "name": "size", "required": false, "type": "integer"}, {"description": "Include progress information of the workflows.", "in": "query", "name": "include_progress", "required": false, "type": "boolean"}, {"description": "Include size information of the workspace.", "in": "query", "name": "include_workspace_size", "required": false, "type": "boolean"}, {"description": "Optional analysis UUID or name to filter.", "in": "query", "name": "workflow_id_or_name", "required": false, "type": "string"}, {"description": "Optional flag to list all shared (owned and unowned) workflows.", "in": "query", "name": "shared", "required": false, "type": "boolean"}, {"description": "Optional argument to list workflows shared by the specified user.", "in": "query", "name": "shared_by", "required": false, "type": "string"}, {"description": "Optional argument to list workflows shared with the specified user.", "in": "query", "name": "shared_with", "required": false, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "Requests succeeded. The response contains the current workflows for a given user.", "examples": {"application/json": [{"created": "2018-06-13T09:47:35.66097", "id": "256b25f4-4cfb-4684-b7a8-73872ef455a1", "launcher_url": "https://github.com/reanahub/reana-demo-helloworld.git", "name": "mytest.1", "size": {"human_readable": "10 MB", "raw": 10490000}, "status": "running", "user": "00000000-0000-0000-0000-000000000000"}, {"created": "2018-06-13T09:47:35.66097", "id": "3c9b117c-d40a-49e3-a6de-5f89fcada5a3", "launcher_url": "https://example.org/specs/reana-snakemake.yaml", "name": "mytest.2", "size": {"human_readable": "12 MB", "raw": 12580000}, "status": "finished", "user": "00000000-0000-0000-0000-000000000000"}, {"created": "2018-06-13T09:47:35.66097", "id": "72e3ee4f-9cd3-4dc7-906c-24511d9f5ee3", "launcher_url": "https://zenodo.org/record/1/reana.yaml", "name": "mytest.3", "size": {"human_readable": "180 KB", "raw": 184320}, "status": "created", "user": "00000000-0000-0000-0000-000000000000"}, {"created": "2018-06-13T09:47:35.66097", "id": "c4c0a1a6-beef-46c7-be04-bf4b3beca5a1", "launcher_url": null, "name": "mytest.4", "size": {"human_readable": "1 GB", "raw": 1074000000}, "status": "created", "user": "00000000-0000-0000-0000-000000000000"}]}, "schema": {"properties": {"items": {"items": {"properties": {"created": {"type": "string"}, "id": {"type": "string"}, "launcher_url": {"type": "string", "x-nullable": true}, "name": {"type": "string"}, "owner_email": {"type": "string"}, "progress": {"type": "object"}, "shared_with": {"items": {"type": "string"}, "type": "array"}, "size": {"properties": {"human_readable": {"type": "string"}, "raw": {"type": "number"}}, "type": "object"}, "status": {"type": "string"}, "user": {"type": "string"}}, "type": "object"}, "type": "array"}, "total": {"type": "integer"}}, "type": "object"}}, "400": {"description": "Request failed. The incoming data specification seems malformed."}, "404": {"description": "Request failed. User does not exist.", "examples": {"application/json": {"message": "User 00000000-0000-0000-0000-000000000000 does not exist"}}}, "500": {"description": "Request failed. Internal controller error.", "examples": {"application/json": {"message": "Internal workflow controller error."}}}}, "summary": "Returns all workflows."}, "post": {"description": "This resource expects all necessary data to represent a workflow so it is stored in database and its workspace is created.", "operationId": "create_workflow", "parameters": [{"description": "Required. UUID of workflow owner.", "in": "query", "name": "user", "required": true, "type": "string"}, {"description": "A root path under which the workflow workspaces are stored.", "in": "query", "name": "workspace_root_path", "required": false, "type": "string"}, {"description": "JSON object including workflow parameters and workflow specification in JSON format (`yadageschemas.load()` output) with necessary data to instantiate a yadage workflow.", "in": "body", "name": "workflow", "required": true, "schema": {"properties": {"git_data": {"description": "GitLab data.", "type": "object"}, "launcher_url": {"description": "Launcher URL.", "type": "string"}, "operational_options": {"description": "Operational options.", "type": "object"}, "reana_specification": {"description": "Workflow specification in JSON format.", "type": "object"}, "retention_rules": {"items": {"additionalProperties": false, "properties": {"retention_days": {"type": "integer"}, "workspace_files": {"type": "string"}}, "title": "Retention rule for the files in the workspace.", "type": "object"}, "title": "Retention rules list for the files in the workspace.", "type": "array"}, "workflow_name": {"description": "Workflow name. If empty name will be generated.", "type": "string"}}, "required": ["reana_specification", "workflow_name", "operational_options", "retention_rules"], "type": "object"}}], "produces": ["application/json"], "responses": {"201": {"description": "Request succeeded. The workflow has been created along with its workspace", "examples": {"application/json": {"message": "Workflow workspace has been created.", "workflow_id": "cdcf48b1-c2f3-4693-8230-b066e088c6ac", "workflow_name": "mytest-1"}}, "schema": {"properties": {"message": {"type": "string"}, "workflow_id": {"type": "string"}, "workflow_name": {"type": "string"}}, "type": "object"}}, "400": {"description": "Request failed. The incoming data specification seems malformed"}, "404": {"description": "Request failed. User does not exist.", "examples": {"application/json": {"message": "User 00000000-0000-0000-0000-000000000000 does not exist"}}}}, "summary": "Create workflow and its workspace."}}, "/api/workflows/move_files/{workflow_id_or_name}": {"put": {"consumes": ["application/json"], "description": "This resource moves files within the workspace. Resource is expecting a workflow UUID.", "operationId": "move_files", "parameters": [{"description": "Required. Analysis UUID or name.", "in": "path", "name": "workflow_id_or_name", "required": true, "type": "string"}, {"description": "Required. Source file(s).", "in": "query", "name": "source", "required": true, "type": "string"}, {"description": "Required. Target file(s).", "in": "query", "name": "target", "required": true, "type": "string"}, {"description": "Required. UUID of workflow owner..", "in": "query", "name": "user", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. Message about successfully moved files is returned.", "examples": {"application/json": {"message": "Files were successfully moved", "workflow_id": "256b25f4-4cfb-4684-b7a8-73872ef455a1", "workflow_name": "mytest.1"}}, "schema": {"properties": {"message": {"type": "string"}, "workflow_id": {"type": "string"}, "workflow_name": {"type": "string"}}, "type": "object"}}, "400": {"description": "Request failed. The incoming payload seems malformed.", "examples": {"application/json": {"message": "Malformed request."}}}, "403": {"description": "Request failed. User is not allowed to access workflow.", "examples": {"application/json": {"message": "User 00000000-0000-0000-0000-000000000000 is not allowed to access workflow 256b25f4-4cfb-4684-b7a8-73872ef455a1"}}}, "404": {"description": "Request failed. Either User or Workflow does not exist.", "examples": {"application/json": {"message": "Workflow 256b25f4-4cfb-4684-b7a8-73872ef455a1 does not exist"}}}, "500": {"description": "Request failed. Internal controller error."}}, "summary": "Move files within workspace."}}, "/api/workflows/{workflow_id_or_name_a}/diff/{workflow_id_or_name_b}": {"get": {"description": "This resource shows the differences between the assets of two workflows. Resource is expecting two workflow UUIDs or names.", "operationId": "get_workflow_diff", "parameters": [{"description": "Required. UUID of workflow owner.", "in": "query", "name": "user", "required": true, "type": "string"}, {"description": "Required. Analysis UUID or name of the first workflow.", "in": "path", "name": "workflow_id_or_name_a", "required": true, "type": "string"}, {"description": "Required. Analysis UUID or name of the second workflow.", "in": "path", "name": "workflow_id_or_name_b", "required": true, "type": "string"}, {"default": false, "description": "Optional flag. If set, file contents are examined.", "in": "query", "name": "brief", "required": false, "type": "boolean"}, {"default": "5", "description": "Optional parameter. Sets number of context lines for workspace diff output.", "in": "query", "name": "context_lines", "required": false, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. Info about a workflow, including the status is returned.", "examples": {"application/json": {"reana_specification": ["- nevents: 100000\n+ nevents: 200000"], "workspace_listing": {"Only in workspace a: code": null}}}, "schema": {"properties": {"reana_specification": {"type": "string"}, "workspace_listing": {"type": "string"}}, "type": "object"}}, "400": {"description": "Request failed. The incoming payload seems malformed.", "examples": {"application/json": {"message": "Malformed request."}}}, "403": {"description": "Request failed. User is not allowed to access workflow.", "examples": {"application/json": {"message": "User 00000000-0000-0000-0000-000000000000 is not allowed to access workflow 256b25f4-4cfb-4684-b7a8-73872ef455a1"}}}, "404": {"description": "Request failed. Either user or workflow does not exist.", "examples": {"application/json": {"message": "Workflow 256b25f4-4cfb-4684-b7a8-73872ef455a1 does not exist."}}}, "500": {"description": "Request failed. Internal controller error."}}, "summary": "Get diff between two workflows."}}, "/api/workflows/{workflow_id_or_name}/close": {"post": {"consumes": ["application/json"], "description": "This resource is expecting a workflow to close an interactive session within its workspace.", "operationId": "close_interactive_session", "parameters": [{"description": "Required. UUID of workflow owner.", "in": "query", "name": "user", "required": true, "type": "string"}, {"description": "Required. Workflow UUID or name.", "in": "path", "name": "workflow_id_or_name", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. The interactive session has been closed.", "examples": {"application/json": {"message": "The interactive session has been closed"}}, "schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}, "400": {"description": "Request failed. The incoming data specification seems malformed.", "examples": {"application/json": {"message": "Malformed request."}}}, "404": {"description": "Request failed. Either User or Workflow does not exist.", "examples": {"application/json": {"message": "Workflow 256b25f4-4cfb-4684-b7a8-73872ef455a1 does not exist"}}}, "500": {"description": "Request failed. Internal controller error."}}, "summary": "Close an interactive workflow session."}}, "/api/workflows/{workflow_id_or_name}/logs": {"get": {"description": "This resource is expecting a workflow UUID and a filename to return its outputs.", "operationId": "get_workflow_logs", "parameters": [{"description": "Required. UUID of workflow owner.", "in": "query", "name": "user", "required": true, "type": "string"}, {"description": "Required. Workflow UUID or name.", "in": "path", "name": "workflow_id_or_name", "required": true, "type": "string"}, {"description": "Steps of a workflow.", "in": "body", "name": "steps", "required": false, "schema": {"description": "List of step names to get logs for.", "items": {"description": "Step name.", "type": "string"}, "type": "array"}}, {"description": "Results page number (pagination).", "in": "query", "name": "page", "required": false, "type": "integer"}, {"description": "Number of results per page (pagination).", "in": "query", "name": "size", "required": false, "type": "integer"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. Info about workflow, including the status is returned.", "examples": {"application/json": {"live_logs_enabled": false, "logs": "{'workflow_logs': string, 'job_logs': { '256b25f4-4cfb-4684-b7a8-73872ef455a2': string, '256b25f4-4cfb-4684-b7a8-73872ef455a3': string, }, 'engine_specific': object, }", "user": "00000000-0000-0000-0000-000000000000", "workflow_id": "256b25f4-4cfb-4684-b7a8-73872ef455a1", "workflow_name": "mytest-1"}}, "schema": {"properties": {"live_logs_enabled": {"type": "boolean"}, "logs": {"type": "string"}, "user": {"type": "string"}, "workflow_id": {"type": "string"}, "workflow_name": {"type": "string"}}, "type": "object"}}, "400": {"description": "Request failed. The incoming data specification seems malformed."}, "404": {"description": "Request failed. User does not exist.", "examples": {"application/json": {"message": "User 00000000-0000-0000-0000-000000000000 does not exist"}}}, "500": {"description": "Request failed. Internal controller error.", "examples": {"application/json": {"message": "Internal workflow controller error."}}}}, "summary": "Returns logs of a specific workflow from a workflow engine."}}, "/api/workflows/{workflow_id_or_name}/open/{interactive_session_type}": {"post": {"consumes": ["application/json"], "description": "This resource is expecting a workflow to start an interactive session within its workspace.", "operationId": "open_interactive_session", "parameters": [{"description": "Required. UUID of workflow owner.", "in": "query", "name": "user", "required": true, "type": "string"}, {"description": "Required. Workflow UUID or name.", "in": "path", "name": "workflow_id_or_name", "required": true, "type": "string"}, {"description": "Optional. Type of interactive session to use, by default Jupyter Notebook.", "in": "path", "name": "interactive_session_type", "required": true, "type": "string"}, {"description": "Interactive session configuration.", "in": "body", "name": "interactive_session_configuration", "required": false, "schema": {"properties": {"image": {"description": "Replaces the default Docker image of an interactive session.", "type": "string"}}, "type": "object"}}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. The interactive session has been opened.", "examples": {"application/json": {"path": "/dd4e93cf-e6d0-4714-a601-301ed97eec60"}}, "schema": {"properties": {"path": {"type": "string"}}, "type": "object"}}, "400": {"description": "Request failed. The incoming data specification seems malformed.", "examples": {"application/json": {"message": "Malformed request."}}}, "404": {"description": "Request failed. Either User or Workflow does not exist.", "examples": {"application/json": {"message": "Workflow 256b25f4-4cfb-4684-b7a8-73872ef455a1 does not exist"}}}, "500": {"description": "Request failed. Internal controller error."}}, "summary": "Start an interactive session inside the workflow workspace."}}, "/api/workflows/{workflow_id_or_name}/parameters": {"get": {"description": "This resource reports the input parameters of workflow.", "operationId": "get_workflow_parameters", "parameters": [{"description": "Required. UUID of workflow owner.", "in": "query", "name": "user", "required": true, "type": "string"}, {"description": "Required. Workflow UUID or name.", "in": "path", "name": "workflow_id_or_name", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. Workflow input parameters, including the status are returned.", "examples": {"application/json": {"id": "dd4e93cf-e6d0-4714-a601-301ed97eec60", "name": "workflow.24", "parameters": {"helloworld": "code/helloworld.py", "inputfile": "data/names.txt", "outputfile": "results/greetings.txt", "sleeptime": 2}, "type": "serial"}}, "schema": {"properties": {"id": {"type": "string"}, "name": {"type": "string"}, "parameters": {"type": "object"}, "type": {"type": "string"}}, "type": "object"}}, "400": {"description": "Request failed. The incoming data specification seems malformed.", "examples": {"application/json": {"message": "Malformed request."}}}, "403": {"description": "Request failed. User is not allowed to access workflow.", "examples": {"application/json": {"message": "User 00000000-0000-0000-0000-000000000000 is not allowed to access workflow 256b25f4-4cfb-4684-b7a8-73872ef455a1"}}}, "404": {"description": "Request failed. Either User or Workflow does not exist.", "examples": {"application/json": {"message": "Workflow 256b25f4-4cfb-4684-b7a8-73872ef455a1 does not exist"}}}, "500": {"description": "Request failed. Internal controller error."}}, "summary": "Get workflow parameters."}}, "/api/workflows/{workflow_id_or_name}/retention_rules": {"get": {"description": "This resource returns all the retention rules of a given workflow.", "operationId": "get_workflow_retention_rules", "parameters": [{"description": "Required. UUID of workflow owner.", "in": "query", "name": "user", "required": true, "type": "string"}, {"description": "Required. Analysis UUID or name.", "in": "path", "name": "workflow_id_or_name", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. The response contains the list of all the retention rules.", "examples": {"application/json": {"retention_rules": [{"apply_on": "2022-11-24T23:59:59", "id": "851da5cf-0b26-40c5-97a1-9acdbb35aac7", "retention_days": 1, "status": "active", "workspace_files": "**/*.tmp"}], "workflow_id": "256b25f4-4cfb-4684-b7a8-73872ef455a1", "workflow_name": "mytest.1"}}, "schema": {"properties": {"retention_rules": {"items": {"properties": {"apply_on": {"type": "string", "x-nullable": true}, "id": {"type": "string"}, "retention_days": {"type": "integer"}, "status": {"type": "string"}, "workspace_files": {"type": "string"}}, "type": "object"}, "type": "array"}, "workflow_id": {"type": "string"}, "workflow_name": {"type": "string"}}, "type": "object"}}, "404": {"description": "Request failed. User or workflow do not exist.", "examples": {"application/json": {"message": "User 00000000-0000-0000-0000-000000000000 does not exist."}}, "schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}, "500": {"description": "Request failed. Internal server error.", "examples": {"application/json": {"message": "Something went wrong."}}, "schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}}, "summary": "Get the retention rules of a workflow."}}, "/api/workflows/{workflow_id_or_name}/share": {"post": {"description": "This resource allows to share a workflow with other users.", "operationId": "share_workflow", "parameters": [{"description": "Required. UUID of workflow owner.", "in": "query", "name": "user", "required": true, "type": "string"}, {"description": "Required. Analysis UUID or name.", "in": "path", "name": "workflow_id_or_name", "required": true, "type": "string"}, {"description": "JSON object with details of the share.", "in": "body", "name": "share_details", "required": true, "schema": {"properties": {"message": {"description": "Optional. Message to include when sharing the workflow.", "type": "string"}, "user_email_to_share_with": {"description": "User to share the workflow with.", "type": "string"}, "valid_until": {"description": "Optional. Date when access to the workflow will expire (format YYYY-MM-DD).", "type": "string"}}, "required": ["user_email_to_share_with"], "type": "object"}}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. The workflow has been shared with the user.", "examples": {"application/json": {"message": "The workflow has been shared with the user.", "workflow_id": "cdcf48b1-c2f3-4693-8230-b066e088c6ac", "workflow_name": "mytest.1"}}, "schema": {"properties": {"message": {"type": "string"}, "workflow_id": {"type": "string"}, "workflow_name": {"type": "string"}}, "type": "object"}}, "400": {"description": "Request failed. The incoming data seems malformed."}, "404": {"description": "Request failed. Workflow does not exist or user does not exist.", "examples": {"application/json": {"message": "Workflow cdcf48b1-c2f3-4693-8230-b066e088c6ac does not exist"}}}, "409": {"description": "Request failed. The workflow is already shared with the user.", "examples": {"application/json": {"message": "The workflow is already shared with the user."}}}, "500": {"description": "Request failed. Internal controller error.", "examples": {"application/json": {"message": "Internal controller error."}}}}, "summary": "Share a workflow with other users."}}, "/api/workflows/{workflow_id_or_name}/share-status": {"get": {"description": "This resource returns the share status of a given workflow.", "operationId": "get_workflow_share_status", "parameters": [{"description": "Required. UUID of workflow owner.", "in": "query", "name": "user", "required": true, "type": "string"}, {"description": "Required. Workflow UUID or name.", "in": "path", "name": "workflow_id_or_name", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. The response contains the share status of the workflow.", "examples": {"application/json": {"shared_with": [{"user_email": "<EMAIL>", "valid_until": "2022-11-24T23:59:59"}], "workflow_id": "256b25f4-4cfb-4684-b7a8-73872ef455a1", "workflow_name": "mytest.1"}}, "schema": {"properties": {"shared_with": {"items": {"properties": {"user_email": {"type": "string"}, "valid_until": {"type": "string", "x-nullable": true}}, "type": "object"}, "type": "array"}, "workflow_id": {"type": "string"}, "workflow_name": {"type": "string"}}, "type": "object"}}, "404": {"description": "Request failed. Workflow does not exist.", "examples": {"application/json": {"message": "Workflow mytest.1 does not exist."}}, "schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}, "500": {"description": "Request failed. Internal server error.", "examples": {"application/json": {"message": "Something went wrong."}}, "schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}}, "summary": "Get the share status of a workflow."}}, "/api/workflows/{workflow_id_or_name}/status": {"get": {"description": "This resource reports the status of workflow.", "operationId": "get_workflow_status", "parameters": [{"description": "Required. UUID of workflow owner.", "in": "query", "name": "user", "required": true, "type": "string"}, {"description": "Required. Workflow UUID or name.", "in": "path", "name": "workflow_id_or_name", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. Info about workflow, including the status is returned.", "examples": {"application/json": {"created": "2018-06-13T09:47:35.66097", "id": "256b25f4-4cfb-4684-b7a8-73872ef455a1", "name": "mytest-1", "status": "running", "user": "00000000-0000-0000-0000-000000000000"}}, "schema": {"properties": {"created": {"type": "string"}, "id": {"type": "string"}, "logs": {"type": "string"}, "name": {"type": "string"}, "progress": {"type": "object"}, "status": {"type": "string"}, "user": {"type": "string"}}, "type": "object"}}, "400": {"description": "Request failed. The incoming data specification seems malformed.", "examples": {"application/json": {"message": "Malformed request."}}}, "403": {"description": "Request failed. User is not allowed to access workflow.", "examples": {"application/json": {"message": "User 00000000-0000-0000-0000-000000000000 is not allowed to access workflow 256b25f4-4cfb-4684-b7a8-73872ef455a1"}}}, "404": {"description": "Request failed. Either User or Workflow does not exist.", "examples": {"application/json": {"message": "Workflow 256b25f4-4cfb-4684-b7a8-73872ef455a1 does not exist"}}}, "500": {"description": "Request failed. Internal controller error."}}, "summary": "Get workflow status."}, "put": {"description": "This resource sets the status of workflow.", "operationId": "set_workflow_status", "parameters": [{"description": "Required. UUID of workflow owner.", "in": "query", "name": "user", "required": true, "type": "string"}, {"description": "Required. Workflow UUID or name.", "in": "path", "name": "workflow_id_or_name", "required": true, "type": "string"}, {"description": "Required. New status.", "enum": ["start", "stop", "deleted"], "in": "query", "name": "status", "required": true, "type": "string"}, {"description": "Optional. Additional parameters to customise the workflow status change.", "in": "body", "name": "parameters", "required": false, "schema": {"properties": {"all_runs": {"description": "Optional. If true, delete all runs of the workflow. Only allowed when status is `deleted`.", "type": "boolean"}, "input_parameters": {"description": "Optional. Additional input parameters that override the ones from the workflow specification. Only allowed when status is `start`.", "type": "object"}, "operational_options": {"description": "Optional. Additional operational options for workflow execution. Only allowed when status is `start`.", "type": "object"}, "restart": {"description": "Optional. If true, the workflow is a restart of an earlier workflow execution. Only allowed when status is `start`.", "type": "boolean"}, "workspace": {"description": "Optional, but must be set to true if provided. If true, delete also the workspace of the workflow. Only allowed when status is `deleted`.", "type": "boolean"}}, "type": "object"}}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. Info about workflow, including the status is returned.", "examples": {"application/json": {"message": "Workflow successfully launched", "status": "running", "user": "00000000-0000-0000-0000-000000000000", "workflow_id": "256b25f4-4cfb-4684-b7a8-73872ef455a1", "workflow_name": "mytest-1"}}, "schema": {"properties": {"message": {"type": "string"}, "status": {"type": "string"}, "user": {"type": "string"}, "workflow_id": {"type": "string"}, "workflow_name": {"type": "string"}}, "type": "object"}}, "400": {"description": "Request failed. The incoming data specification seems malformed.", "examples": {"application/json": {"message": "Malformed request."}}}, "403": {"description": "Request failed. User is not allowed to access workflow.", "examples": {"application/json": {"message": "User 00000000-0000-0000-0000-000000000000 is not allowed to access workflow 256b25f4-4cfb-4684-b7a8-73872ef455a1"}}}, "404": {"description": "Request failed. Either User or Workflow does not exist.", "examples": {"application/json": {"message": "Workflow 256b25f4-4cfb-4684-b7a8-73872ef455a1 does not exist"}}}, "409": {"description": "Request failed. The workflow could not be started due to a conflict.", "examples": {"application/json": {"message": "Workflow 256b25f4-4cfb-4684-b7a8-73872ef455a1 could not be started because it is already running."}}}, "500": {"description": "Request failed. Internal controller error."}, "501": {"description": "Request failed. The specified status change is not implemented.", "examples": {"application/json": {"message": "Status resume is not supported yet."}}}, "502": {"description": "Request failed. Connection to a third party system has failed.", "examples": {"application/json": {"message": "Connection to database timed out, please retry."}}}}, "summary": "Set workflow status."}}, "/api/workflows/{workflow_id_or_name}/unshare": {"post": {"description": "This resource allows to unshare a workflow with other users.", "operationId": "unshare_workflow", "parameters": [{"description": "Required. UUID of workflow owner.", "in": "query", "name": "user", "required": true, "type": "string"}, {"description": "Required. Analysis UUID or name.", "in": "path", "name": "workflow_id_or_name", "required": true, "type": "string"}, {"description": "Required. User to unshare the workflow with.", "in": "query", "name": "user_email_to_unshare_with", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. The workflow has been unshared with the user.", "examples": {"application/json": {"message": "The workflow has been unsahred with the user.", "workflow_id": "cdcf48b1-c2f3-4693-8230-b066e088c6ac", "workflow_name": "mytest.1"}}, "schema": {"properties": {"message": {"type": "string"}, "workflow_id": {"type": "string"}, "workflow_name": {"type": "string"}}, "type": "object"}}, "400": {"description": "Request failed. The incoming data specification seems malformed.", "examples": {"application/json": {"message": "Malformed request."}}, "schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}, "403": {"description": "Request failed. User is not allowed to unshare the workflow.", "examples": {"application/json": {"message": "User is not allowed to unshare the workflow."}}, "schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}, "404": {"description": "Request failed. Workflow does not exist or user does not exist.", "examples": {"application/json": {"message": "Workflow cdcf48b1-c2f3-4693-8230-b066e088c6ac does not exist"}}, "schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}, "409": {"description": "Request failed. The workflow is not shared with the user.", "examples": {"application/json": {"message": "The workflow is not shared with the user."}}, "schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}, "500": {"description": "Request failed. Internal controller error.", "examples": {"application/json": {"message": "Internal controller error."}}, "schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}}, "summary": "Unshare a workflow with other users."}}, "/api/workflows/{workflow_id_or_name}/workspace": {"get": {"description": "This resource retrieves the file list of a workspace, given its workflow UUID.", "operationId": "get_files", "parameters": [{"description": "Required. UUID of workflow owner.", "in": "query", "name": "user", "required": true, "type": "string"}, {"description": "Required. Workflow UUID or name.", "in": "path", "name": "workflow_id_or_name", "required": true, "type": "string"}, {"description": "File name(s) (glob) to list.", "in": "query", "name": "file_name", "required": false, "type": "string"}, {"description": "Results page number (pagination).", "in": "query", "name": "page", "required": false, "type": "integer"}, {"description": "Number of results per page (pagination).", "in": "query", "name": "size", "required": false, "type": "integer"}, {"description": "Filter workflow workspace files.", "in": "query", "name": "search", "required": false, "type": "string"}], "produces": ["multipart/form-data"], "responses": {"200": {"description": "Requests succeeded. The list of code|input|output files has been retrieved.", "schema": {"properties": {"items": {"items": {"properties": {"last-modified": {"type": "string"}, "name": {"type": "string"}, "size": {"properties": {"human_readable": {"type": "string"}, "raw": {"type": "number"}}, "type": "object"}}, "type": "object"}, "type": "array"}, "total": {"type": "integer"}}, "type": "object"}}, "400": {"description": "Request failed. The incoming data specification seems malformed."}, "404": {"description": "Request failed. Workflow does not exist.", "examples": {"application/json": {"message": "Workflow 256b25f4-4cfb-4684-b7a8-73872ef455a1 does not exist."}}}, "500": {"description": "Request failed. Internal controller error.", "examples": {"application/json": {"message": "Internal workflow controller error."}}}}, "summary": "Returns the workspace file list."}, "post": {"consumes": ["application/octet-stream"], "description": "This resource is expecting a workflow UUID and a file to place in the workspace.", "operationId": "upload_file", "parameters": [{"description": "Required. UUID of workflow owner.", "in": "query", "name": "user", "required": true, "type": "string"}, {"description": "Required. Workflow UUID or name.", "in": "path", "name": "workflow_id_or_name", "required": true, "type": "string"}, {"description": "Required. File to add to the workspace.", "in": "body", "name": "file", "required": true, "schema": {"type": "string"}}, {"description": "Required. File name.", "in": "query", "name": "file_name", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. The file has been added to the workspace.", "examples": {"application/json": {"message": "`file_name` has been successfully uploaded."}}, "schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}, "400": {"description": "Request failed. The incoming data specification seems malformed"}, "404": {"description": "Request failed. Workflow does not exist.", "examples": {"application/json": {"message": "Workflow cdcf48b1-c2f3-4693-8230-b066e088c6ac does not exist"}}, "schema": {"properties": {"message": {"type": "string"}}, "type": "object"}}, "500": {"description": "Request failed. Internal controller error."}}, "summary": "Adds a file to the workspace."}}, "/api/workflows/{workflow_id_or_name}/workspace/{file_name}": {"delete": {"description": "This resource is expecting a workflow UUID and a filename existing inside the workspace to be deleted.", "operationId": "delete_file", "parameters": [{"description": "Required. UUID of workflow owner.", "in": "query", "name": "user", "required": true, "type": "string"}, {"description": "Required. Workflow UUID or name", "in": "path", "name": "workflow_id_or_name", "required": true, "type": "string"}, {"description": "Required. Name (or path) of the file to be deleted.", "in": "path", "name": "file_name", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "Request succeeded. Details about deleted files and failed deletions are returned.", "schema": {"type": "file"}}, "404": {"description": "Request failed. `file_name` does not exist.", "examples": {"application/json": {"message": "input.csv does not exist"}}}, "500": {"description": "Request failed. Internal controller error.", "examples": {"application/json": {"message": "Internal workflow controller error."}}}}, "summary": "Delete the specified file."}, "get": {"description": "This resource is expecting a workflow UUID and a filename existing inside the workspace to return its content.", "operationId": "download_file", "parameters": [{"description": "Required. UUID of workflow owner.", "in": "query", "name": "user", "required": true, "type": "string"}, {"description": "Required. Workflow UUID or name", "in": "path", "name": "workflow_id_or_name", "required": true, "type": "string"}, {"description": "Required. Name (or path) of the file to be downloaded.", "in": "path", "name": "file_name", "required": true, "type": "string"}, {"description": "Optional flag to return a previewable response of the file (corresponding mime-type).", "in": "query", "name": "preview", "required": false, "type": "boolean"}], "produces": ["application/octet-stream", "application/json", "application/zip", "image/*", "text/html"], "responses": {"200": {"description": "Requests succeeded. The file has been downloaded.", "schema": {"type": "file"}}, "400": {"description": "Request failed. The incoming data specification seems malformed."}, "404": {"description": "Request failed. `file_name` does not exist.", "examples": {"application/json": {"message": "input.csv does not exist"}}}, "500": {"description": "Request failed. Internal controller error.", "examples": {"application/json": {"message": "Internal workflow controller error."}}}}, "summary": "Returns the requested file."}}}, "swagger": "2.0"}