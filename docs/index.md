```{include} ../README.md
:end-before: "## About"
```

```{include} ../README.md
:start-after: "## About"
:end-before: "## Useful links"

```

## Configuration

```{eval-rst}
.. automodule:: reana_commons.config
   :members:
```

## API

### REANA API client

```{eval-rst}
.. automodule:: reana_commons.api_client
   :members:
```

### REANA Kubernetes API client

```{eval-rst}
.. automodule:: reana_commons.k8s.api_client
   :members:
```

```{eval-rst}
.. automodule:: reana_commons.k8s.volumes
   :members:
```

### REANA AMQP Publisher

```{eval-rst}
.. automodule:: reana_commons.publisher
   :members:

```

### REANA AMQP Consumer

```{eval-rst}
.. automodule:: reana_commons.consumer
   :members:

```

### REANA Serial workflow utilities

```{eval-rst}
.. automodule:: reana_commons.serial
   :members:

```

### REANA utilities

```{eval-rst}
.. automodule:: reana_commons.utils
   :members:
```

### REANA errors

```{eval-rst}
.. automodule:: reana_commons.errors
   :members:

```

```{include} ../CHANGELOG.md
:heading-offset: 1
```

```{include} ../CONTRIBUTING.md
:heading-offset: 1
```

## License

```{eval-rst}
.. include:: ../LICENSE
```

In applying this license, CERN does not waive the privileges and immunities
granted to it by virtue of its status as an Intergovernmental Organization or
submit itself to any jurisdiction.

```{include} ../AUTHORS.md
:heading-offset: 1
```
