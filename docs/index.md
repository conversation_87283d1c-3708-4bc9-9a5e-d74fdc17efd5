<!-- markdownlint-disable MD041 -->
<!-- markdownlint-disable MD033 -->

```{include} ../README.md
:end-before: "## About"
```

```{include} ../README.md
:start-after: "## About"
:end-before: "## Useful links"
```

## REST API

The REANA-Workflow-Controller component offers a REST API for managing
workflows. Detailed REST API documentation can be found
<a href="_static/api.html">here</a>.

```{eval-rst}
.. automodule:: reana_workflow_controller.rest.workflows
   :members:
```

```{eval-rst}
.. automodule:: reana_workflow_controller.rest.workflows_session
   :members:
```

```{eval-rst}
.. automodule:: reana_workflow_controller.rest.workflows_status
   :members:
```

```{eval-rst}
.. automodule:: reana_workflow_controller.rest.workflows_workspace
   :members:
```

```{include} ../CHANGELOG.md
:heading-offset: 1
```

```{include} ../CONTRIBUTING.md
:heading-offset: 1
```

## License

```{eval-rst}
.. include:: ../LICENSE
```

In applying this license, CERN does not waive the privileges and immunities
granted to it by virtue of its status as an Intergovernmental Organization or
submit itself to any jurisdiction.

```{include} ../AUTHORS.md
:heading-offset: 1
```
