% The title is set to `REANA-DB docs` instead of `REANA-DB` as this is a
% workaround to avoid having multiple HTML objects with the same id/anchor,
% as the tile clashes with the CLI API documentation generated by sphinx_click
# REANA-DB docs

```{include} ../README.md
:start-line: 1
:end-before: "## About"
```

```{include} ../README.md
:start-after: "## About"
:end-before: "## Useful links"

```

## Configuration

```{eval-rst}
.. automodule:: reana_db.config
   :members:

```

## API

### Database management

```{eval-rst}
.. automodule:: reana_db.database
   :members:
```

### Models

```{eval-rst}
.. automodule:: reana_db.models
   :members:
```

### Utilities

```{eval-rst}
.. automodule:: reana_db.utils
   :members:

```

## CLI API

```{eval-rst}
.. click:: reana_db.cli:cli
   :prog: reana-db
   :show-nested:
```

```{include} ../CHANGELOG.md
:heading-offset: 1
```

```{include} ../CONTRIBUTING.md
:heading-offset: 1

```

## License

```{eval-rst}
.. include:: ../LICENSE
```

In applying this license, CERN does not waive the privileges and immunities
granted to it by virtue of its status as an Intergovernmental Organization or
submit itself to any jurisdiction.

```{include} ../AUTHORS.md
:heading-offset: 1
```
