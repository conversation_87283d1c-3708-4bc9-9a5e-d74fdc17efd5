# Contributing

Bug reports, issues, feature requests, and other contributions are welcome. If
you find a demonstrable problem that is caused by the REANA code, please:

1. Search for
   [already reported problems](https://github.com/reanahub/reana-workflow-controller/issues).
2. Check if the issue has been fixed or is still reproducible on the latest
   `master` branch.
3. Create an issue, ideally with **a test case**.

If you create a pull request fixing a bug or implementing a feature, you can run
the tests to ensure that everything is operating correctly:

```console
$ ./run-tests.sh
```

Each pull request should preserve or increase code coverage.
