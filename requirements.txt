#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile --annotation-style=line --output-file=requirements.txt setup.py
#
alembic==1.14.1           # via reana-db
amqp==5.3.1               # via kombu
appdirs==1.4.4            # via fs
arrow==1.3.0              # via isoduration
attrs==25.1.0             # via jsonschema, referencing
bracex==2.5.post1         # via wcmatch
bravado==10.3.2           # via reana-commons
bravado-core==6.1.0       # via bravado, reana-commons
cachetools==5.5.1         # via google-auth
certifi==2025.1.31        # via kubernetes, opensearch-py, requests
cffi==1.17.1              # via cryptography
charset-normalizer==3.4.1  # via requests
checksumdir==1.1.9        # via reana-commons
click==8.1.8              # via flask, reana-commons
cryptography==44.0.0      # via sqlalchemy-utils
events==0.5               # via opensearch-py
flask==2.2.5              # via reana-workflow-controller (setup.py)
fqdn==1.5.1               # via jsonschema
fs==2.4.16                # via reana-commons
gherkin-official==31.0.0  # via reana-commons
gitdb==4.0.12             # via gitpython
gitpython==3.1.44         # via reana-workflow-controller (setup.py)
google-auth==2.38.0       # via kubernetes
greenlet==3.1.1           # via sqlalchemy
idna==3.10                # via jsonschema, requests
importlib-resources==6.5.2  # via swagger-spec-validator
isoduration==20.11.0      # via jsonschema
itsdangerous==2.2.0       # via flask
jinja2==3.1.5             # via flask
jsonpickle==4.0.1         # via reana-workflow-controller (setup.py)
jsonpointer==3.0.0        # via jsonschema
jsonref==1.1.0            # via bravado-core
jsonschema[format]==4.23.0  # via bravado-core, reana-commons, swagger-spec-validator
jsonschema-specifications==2024.10.1  # via jsonschema
kombu==5.4.2              # via reana-commons
kubernetes==22.6.0        # via reana-commons
mako==1.3.8               # via alembic
markupsafe==3.0.2         # via jinja2, mako, werkzeug
marshmallow==2.21.0       # via reana-workflow-controller (setup.py), webargs
mock==3.0.5               # via reana-commons
monotonic==1.6            # via bravado
msgpack==1.1.0            # via bravado-core
msgpack-python==0.5.6     # via bravado
oauthlib==3.2.2           # via requests-oauthlib
opensearch-py==2.7.1      # via reana-workflow-controller (setup.py)
packaging==24.2           # via reana-workflow-controller (setup.py)
parse==1.20.2             # via reana-commons
psycopg2-binary==2.9.10   # via reana-db
pyasn1==0.6.1             # via pyasn1-modules, rsa
pyasn1-modules==0.4.1     # via google-auth
pycparser==2.22           # via cffi
python-dateutil==2.9.0.post0  # via arrow, bravado, bravado-core, kubernetes, opensearch-py
pytz==2025.1              # via bravado-core
pyyaml==6.0.2             # via bravado, bravado-core, kubernetes, reana-commons, swagger-spec-validator
reana-commons[kubernetes]==0.95.0a7  # via reana-db, reana-workflow-controller (setup.py)
reana-db==0.95.0a5        # via reana-workflow-controller (setup.py)
referencing==0.36.2       # via jsonschema, jsonschema-specifications
requests==2.32.3          # via bravado, bravado-core, kubernetes, opensearch-py, reana-workflow-controller (setup.py), requests-oauthlib
requests-oauthlib==2.0.0  # via kubernetes
rfc3339-validator==0.1.4  # via jsonschema
rfc3987==1.3.8            # via jsonschema
rpds-py==0.22.3           # via jsonschema, referencing
rsa==4.9                  # via google-auth
simplejson==3.19.3        # via bravado, bravado-core
six==1.17.0               # via bravado, bravado-core, fs, kubernetes, mock, python-dateutil, rfc3339-validator
smmap==5.0.2              # via gitdb
sqlalchemy==1.4.54        # via alembic, reana-db, sqlalchemy-utils
sqlalchemy-utils[encrypted]==0.41.2  # via reana-db, reana-workflow-controller (setup.py)
swagger-spec-validator==3.0.4  # via bravado-core
types-python-dateutil==2.9.0.20241206  # via arrow
typing-extensions==4.12.2  # via alembic, bravado, gherkin-official, referencing, swagger-spec-validator
tzdata==2025.1            # via kombu
uri-template==1.3.0       # via jsonschema
urllib3==2.3.0            # via kubernetes, opensearch-py, requests
uwsgi==2.0.28             # via reana-workflow-controller (setup.py)
uwsgi-tools==1.1.1        # via reana-workflow-controller (setup.py)
uwsgitop==0.12            # via reana-workflow-controller (setup.py)
vine==5.1.0               # via amqp, kombu
wcmatch==8.4.1            # via reana-commons
webargs==6.1.1            # via reana-workflow-controller (setup.py)
webcolors==24.11.1        # via jsonschema
websocket-client==1.8.0   # via kubernetes
werkzeug==2.2.3           # via flask, reana-commons, reana-workflow-controller (setup.py)

# The following packages are considered to be unsafe in a requirements file:
# setuptools
