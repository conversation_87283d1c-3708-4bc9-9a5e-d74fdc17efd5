# REANA-Workflow-Controller

[![image](https://github.com/reanahub/reana-workflow-controller/workflows/CI/badge.svg)](https://github.com/reanahub/reana-workflow-controller/actions)
[![image](https://readthedocs.org/projects/reana-workflow-controller/badge/?version=latest)](https://reana-workflow-controller.readthedocs.io/en/latest/?badge=latest)
[![image](https://codecov.io/gh/reanahub/reana-workflow-controller/branch/master/graph/badge.svg)](https://codecov.io/gh/reanahub/reana-workflow-controller)
[![image](https://img.shields.io/badge/discourse-forum-blue.svg)](https://forum.reana.io)
[![image](https://img.shields.io/github/license/reanahub/reana-workflow-controller.svg)](https://github.com/reanahub/reana-workflow-controller/blob/master/LICENSE)
[![image](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

## About

REANA-Workflow-Controller is a component of the [REANA](http://www.reana.io/)
reusable and reproducible research data analysis platform. It takes care of
instantiating and managing computational workflows.

## Features

- start workflow execution
- control workflow steps
- support for several workflow specifications (CWL, Yadage, Serial)

## Usage

The detailed information on how to install and use REANA can be found in
[docs.reana.io](https://docs.reana.io).

## Useful links

- [REANA project home page](http://www.reana.io/)
- [REANA user documentation](https://docs.reana.io)
- [REANA user support forum](https://forum.reana.io)
- [REANA-Workflow-Controller releases](https://reana-workflow-controller.readthedocs.io/en/latest#changes)
- [REANA-Workflow-Controller docker images](https://hub.docker.com/r/reanahub/reana-workflow-controller)
- [REANA-Workflow-Controller developer documentation](https://reana-workflow-controller.readthedocs.io/)
- [REANA-Workflow-Controller known issues](https://github.com/reanahub/reana-workflow-controller/issues)
- [REANA-Workflow-Controller source code](https://github.com/reanahub/reana-workflow-controller)
