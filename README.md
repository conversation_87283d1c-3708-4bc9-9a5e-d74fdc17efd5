# REANA-Commons

[![image](https://img.shields.io/pypi/pyversions/reana-commons.svg)](https://pypi.org/pypi/reana-commons)
[![image](https://github.com/reanahub/reana-commons/workflows/CI/badge.svg)](https://github.com/reanahub/reana-commons/actions)
[![image](https://readthedocs.org/projects/reana-commons/badge/?version=latest)](https://reana-commons.readthedocs.io/en/latest/?badge=latest)
[![image](https://codecov.io/gh/reanahub/reana-commons/branch/master/graph/badge.svg)](https://codecov.io/gh/reanahub/reana-commons)
[![image](https://img.shields.io/badge/discourse-forum-blue.svg)](https://forum.reana.io)
[![image](https://img.shields.io/github/license/reanahub/reana-commons.svg)](https://github.com/reanahub/reana-commons/blob/master/LICENSE)
[![image](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

## About

REANA-Commons is a component of the [REANA](http://www.reana.io/) reusable and
reproducible research data analysis platform. It provides common utilities and schemas
shared by the REANA cluster components.

## Features

- common API clients for internal communication
- centralised OpenAPI specifications for REANA components
- AMQP connection management and communication
- utility functions for cluster components

## Usage

The detailed information on how to install and use REANA can be found in
[docs.reana.io](https://docs.reana.io).

## Useful links

- [REANA project home page](http://www.reana.io/)
- [REANA user documentation](https://docs.reana.io)
- [REANA user support forum](https://forum.reana.io)
- [REANA-Commons releases](https://reana-commons.readthedocs.io/en/latest#changes)
- [REANA-Commons developer documentation](https://reana-commons.readthedocs.io/)
- [REANA-Commons known issues](https://github.com/reanahub/reana-commons/issues)
- [REANA-Commons source code](https://github.com/reanahub/reana-commons)
