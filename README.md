# REANA DB

[![image](https://img.shields.io/pypi/pyversions/reana-db.svg)](https://pypi.org/pypi/reana-db)
[![image](https://github.com/reanahub/reana-db/workflows/CI/badge.svg)](https://github.com/reanahub/reana-db/actions)
[![image](https://readthedocs.org/projects/reana-db/badge/?version=latest)](https://reana-db.readthedocs.io/en/latest/?badge=latest)
[![image](https://codecov.io/gh/reanahub/reana-db/branch/master/graph/badge.svg)](https://codecov.io/gh/reanahub/reana-db)
[![image](https://img.shields.io/badge/discourse-forum-blue.svg)](https://forum.reana.io)
[![image](https://img.shields.io/github/license/reanahub/reana-db.svg)](https://github.com/reanahub/reana-db/blob/master/LICENSE)
[![image](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

## About

REANA-DB is a component of the [REANA](http://www.reana.io/) reusable analysis platform.
It contains REANA database models and utilities.

## Features

- database persistence for REANA system
- database models and utilities
- database upgrades and migrations

## Usage

The detailed information on how to install and use REANA can be found in
[docs.reana.io](https://docs.reana.io).

## Useful links

- [REANA project home page](http://www.reana.io/)
- [REANA user documentation](https://docs.reana.io)
- [REANA user support forum](https://forum.reana.io)
- [REANA-DB releases](https://reana-db.readthedocs.io/en/latest#changes)
- [REANA-DB developer documentation](https://reana-db.readthedocs.io/)
- [REANA-DB known issues](https://github.com/reanahub/reana-db/issues)
- [REANA-DB source code](https://github.com/reanahub/reana-db)
